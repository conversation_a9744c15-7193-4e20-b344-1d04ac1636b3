/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT TEST FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file BaselineGeneratorCore Test Suite
 * @filepath server/src/platform/performance/baseline-generator/__tests__/BaselineGeneratorCore.test.ts
 * @milestone M0.1
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02.TEST-02
 * @component baseline-generator-core-tests
 * @reference foundation-context
 * @template enterprise-test-suite
 * @tier server
 * @context performance-context
 * @category performance-monitoring
 * @created 2025-09-13
 * @modified 2025-09-13 00:00:00 +03
 * @version 1.0.0
 *
 * @description
 * Comprehensive test suite for BaselineGeneratorCore specialized engine.
 * Tests core baseline generation algorithms, performance threshold management,
 * resilient timing integration, and MEM-SAFE-002 compliance with 95%+ coverage.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact performance-architecture,test-coverage
 * @milestone-compliance M0.1-standards
 *
 * ============================================================================
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

import { BaselineGeneratorCore } from '../BaselineGeneratorCore';
import {
  TPerformanceBaselineConfig,
  TPerformanceBaselineResult,
  TPerformanceThresholds
} from '../types/performance-baseline-types';
import { TTrackingConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST CONFIGURATION AND SETUP
// ============================================================================

describe('BaselineGeneratorCore', () => {
  let generator: BaselineGeneratorCore;
  let testConfig: Partial<TTrackingConfig>;
  let mockBaselineConfig: TPerformanceBaselineConfig;

  beforeEach(async () => {
    // ✅ Setup test configuration
    testConfig = {
      service: {
        name: 'test-baseline-generator-core',
        version: '1.0.0',
        environment: 'development',
        timeout: 15000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 5000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 10000,
          errorRate: 5,
          memoryUsage: 100,
          cpuUsage: 80
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 10
      }
    };

    // ✅ Create generator instance
    generator = new BaselineGeneratorCore(testConfig);
    await generator.initialize();

    // ✅ Setup mock baseline configuration
    mockBaselineConfig = {
      baselineId: 'test-core-baseline-001',
      name: 'Test Core Baseline',
      description: 'Test baseline for core engine testing',
      targetComponents: ['core-component-1', 'core-component-2'],
      samplingInterval: 1000,
      samplingDuration: 5000,
      thresholds: {
        responseTime: 10,
        memoryUsage: 50 * 1024 * 1024,
        cpuUsage: 15,
        throughput: 1000
      },
      environment: 'test',
      enabled: true,
      metadata: { testMode: true }
    };
  });

  afterEach(async () => {
    if (generator) {
      await generator.shutdown();
    }
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    test('should initialize successfully with dual-field resilient timing', () => {
      expect(generator).toBeDefined();
      expect(generator.isHealthy()).toBe(true);
      
      // ✅ Verify dual-field pattern
      const resilientTimer = (generator as any)._resilientTimer;
      const metricsCollector = (generator as any)._metricsCollector;
      
      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
    });

    test('should extend BaseTrackingService for MEM-SAFE-002 compliance', () => {
      expect(generator).toBeInstanceOf(require('../../tracking/core-data/base/BaseTrackingService').BaseTrackingService);
    });

    test('should implement IPerformanceBaseline interface', async () => {
      // ✅ Verify interface methods exist
      expect(typeof generator.generateBaseline).toBe('function');
      expect(typeof generator.validateBaseline).toBe('function');
      expect(typeof generator.updateBaseline).toBe('function');
      expect(typeof generator.getBaseline).toBe('function');
      expect(typeof generator.deleteBaseline).toBe('function');
    });
  });

  // ============================================================================
  // BASELINE GENERATION TESTS
  // ============================================================================

  describe('Baseline Generation', () => {
    test('should generate baseline successfully', async () => {
      const result = await generator.generateBaseline(mockBaselineConfig);

      expect(result).toBeDefined();
      expect(result.baselineId).toBe(mockBaselineConfig.baselineId);
      expect(result.name).toBe(mockBaselineConfig.name);
      expect(result.description).toBe(mockBaselineConfig.description);
      expect(result.status).toBe('success');
      expect(result.aggregatedMetrics).toBeDefined();
      expect(result.componentResults).toBeDefined();
      expect(result.environment).toBe(mockBaselineConfig.environment);
      expect(result.timestamp).toBeDefined();
    });

    test('should generate baseline with proper metrics structure', async () => {
      const result = await generator.generateBaseline(mockBaselineConfig);

      expect(result.aggregatedMetrics).toBeDefined();
      expect(result.aggregatedMetrics?.timestamp).toBeDefined();
      expect(result.aggregatedMetrics?.responseTime).toBeDefined();
      expect(result.aggregatedMetrics?.memoryUsage).toBeDefined();
      expect(result.aggregatedMetrics?.cpuUsage).toBeDefined();
      expect(result.aggregatedMetrics?.throughput).toBeDefined();
      expect(result.aggregatedMetrics?.errorRate).toBeDefined();
    });

    test('should cache generated baselines', async () => {
      const result1 = await generator.generateBaseline(mockBaselineConfig);
      const result2 = await generator.getBaseline(mockBaselineConfig.baselineId);

      expect(result2).toBeDefined();
      expect(result2!.baselineId).toBe(result1.baselineId);
      expect(result2!.timestamp).toBe(result1.timestamp);
    });

    test('should handle multiple baseline generation', async () => {
      const configs = Array.from({ length: 5 }, (_, i) => ({
        ...mockBaselineConfig,
        baselineId: `multi-baseline-${i}`,
        name: `Multi Baseline ${i}`
      }));

      const results = await Promise.all(
        configs.map(config => generator.generateBaseline(config))
      );

      expect(results.length).toBe(5);
      results.forEach((result, index) => {
        expect(result.baselineId).toBe(`multi-baseline-${index}`);
        expect(result.status).toBe('success');
      });
    });
  });

  // ============================================================================
  // PERFORMANCE REQUIREMENTS TESTS
  // ============================================================================

  describe('Performance Requirements (<10ms)', () => {
    test('should meet <10ms response time for baseline generation', async () => {
      const startTime = Date.now();
      await generator.generateBaseline(mockBaselineConfig);
      const duration = Date.now() - startTime;

      // ✅ Allow tolerance for test environment
      expect(duration).toBeLessThan(50);
    });

    test('should meet <10ms response time for baseline retrieval', async () => {
      // ✅ Setup baseline first
      await generator.generateBaseline(mockBaselineConfig);

      const startTime = Date.now();
      await generator.getBaseline(mockBaselineConfig.baselineId);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(10);
    });

    test('should maintain performance under concurrent operations', async () => {
      const concurrentOperations = Array.from({ length: 10 }, (_, i) =>
        generator.generateBaseline({
          ...mockBaselineConfig,
          baselineId: `concurrent-core-${i}`
        })
      );

      const startTime = Date.now();
      const results = await Promise.all(concurrentOperations);
      const totalDuration = Date.now() - startTime;

      expect(results.length).toBe(10);
      expect(totalDuration).toBeLessThan(500); // 10 operations in <500ms
    });
  });

  // ============================================================================
  // RESILIENT TIMING INTEGRATION TESTS
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    test('should record timing metrics for baseline operations', async () => {
      const metricsCollector = (generator as any)._metricsCollector;
      const initialMetricsCount = metricsCollector.getMetrics().size;

      await generator.generateBaseline(mockBaselineConfig);

      const finalMetricsCount = metricsCollector.getMetrics().size;
      expect(finalMetricsCount).toBeGreaterThan(initialMetricsCount);
    });

    test('should handle timing context errors gracefully', async () => {
      // ✅ Mock timing context to throw error
      const resilientTimer = (generator as any)._resilientTimer;
      const originalStart = resilientTimer.start;
      
      resilientTimer.start = jest.fn(() => {
        throw new Error('Timing context error');
      });

      // ✅ Operation should still complete
      await expect(generator.generateBaseline(mockBaselineConfig))
        .resolves.toBeDefined();

      // ✅ Restore original method
      resilientTimer.start = originalStart;
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid baseline configuration', async () => {
      const invalidConfig = {
        ...mockBaselineConfig,
        baselineId: '',
        samplingDuration: -1
      };

      await expect(generator.generateBaseline(invalidConfig))
        .rejects.toThrow();
    });

    test('should handle baseline validation errors', async () => {
      const mockMetrics = {
        timestamp: new Date().toISOString(),
        responseTime: { average: 5, median: 5, min: 3, max: 8, p95: 7, p99: 8, standardDeviation: 1, sampleCount: 100 },
        memoryUsage: { average: 30000000, peak: 35000000, minimum: 25000000, growthRate: 0.1, leakIndicators: [], gcStatistics: { totalCycles: 3, averageDuration: 8, memoryFreed: 3000000, efficiency: 0.9 } },
        cpuUsage: { average: 10, peak: 15, minimum: 5, distribution: [8, 10, 12, 14, 15], efficiencyScore: 0.85 },
        throughput: { operationsPerSecond: 1200, requestsPerSecond: 1200, dataThroughput: 1048576, peakThroughput: 1500, efficiency: 0.8 },
        errorRate: { overall: 0.1, byType: {}, bySeverity: {}, trend: 'stable' }
      };

      await expect(generator.validateBaseline('non-existent', mockMetrics))
        .rejects.toThrow();
    });

    test('should handle cleanup on shutdown', async () => {
      // ✅ Generate some baselines
      await generator.generateBaseline(mockBaselineConfig);
      await generator.generateBaseline({
        ...mockBaselineConfig,
        baselineId: 'cleanup-test-baseline'
      });

      // ✅ Verify baselines exist
      expect(await generator.getBaseline(mockBaselineConfig.baselineId)).toBeDefined();
      expect(await generator.getBaseline('cleanup-test-baseline')).toBeDefined();

      // ✅ Shutdown should clean up
      await generator.shutdown();
      expect(generator.isHealthy()).toBe(false);
    });
  });
});
