/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT TEST FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file BaselineAnalysisEngine Test Suite
 * @filepath server/src/platform/performance/baseline-generator/__tests__/BaselineAnalysisEngine.test.ts
 * @milestone M0.1
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02.TEST-04
 * @component baseline-analysis-engine-tests
 * @reference foundation-context
 * @template enterprise-test-suite
 * @tier server
 * @context performance-context
 * @category performance-monitoring
 * @created 2025-09-13
 * @modified 2025-09-13 00:00:00 +03
 * @version 1.0.0
 *
 * @description
 * Comprehensive test suite for BaselineAnalysisEngine specialized engine.
 * Tests statistical analysis capabilities, trend detection, resilient timing
 * integration, and MEM-SAFE-002 compliance with 95%+ coverage target.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact performance-architecture,test-coverage
 * @milestone-compliance M0.1-standards
 *
 * ============================================================================
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

import { BaselineAnalysisEngine } from '../BaselineAnalysisEngine';
import {
  TAnalysisConfig,
  TAnalysisResult,
  TPerformanceMetricsData,
  TTrendAnalysis,
  TStatisticalSummary
} from '../types/performance-baseline-types';
import { TTrackingConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST CONFIGURATION AND SETUP
// ============================================================================

describe('BaselineAnalysisEngine', () => {
  let analyzer: BaselineAnalysisEngine;
  let testConfig: TTrackingConfig;
  let mockAnalysisConfig: TAnalysisConfig;
  let mockMetricsData: TPerformanceMetricsData[];

  beforeEach(async () => {
    // ✅ Setup test configuration
    testConfig = {
      service: {
        name: 'test-analysis-engine',
        version: '1.0.0',
        environment: 'development',
        timeout: 15000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 5000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 10000,
          errorRate: 5,
          memoryUsage: 100,
          cpuUsage: 80
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 10
      }
    };

    // ✅ Create analyzer instance
    analyzer = new BaselineAnalysisEngine(testConfig);
    await analyzer.initialize();

    // ✅ Setup mock metrics data
    mockMetricsData = [
      {
        timestamp: new Date(Date.now() - 3000).toISOString(),
        responseTime: { average: 8.5, median: 8.0, min: 5.0, max: 15.0, p95: 12.0, p99: 14.0, standardDeviation: 2.5, sampleCount: 100 },
        memoryUsage: { average: 40000000, peak: 45000000, minimum: 35000000, growthRate: 0.1, leakIndicators: [], gcStatistics: { totalCycles: 5, averageDuration: 10, memoryFreed: 5000000, efficiency: 0.9 } },
        cpuUsage: { average: 12.5, peak: 18.0, minimum: 8.0, distribution: [10, 12, 14, 16, 18], efficiencyScore: 0.85 },
        throughput: { operationsPerSecond: 1200, requestsPerSecond: 1200, dataThroughput: 1048576, peakThroughput: 1500, efficiency: 0.8 },
        errorRate: { overall: 0.5, byType: { 'timeout': 0.3, 'network': 0.2 }, bySeverity: { 'low': 0.4, 'medium': 0.1 }, trend: 'stable' }
      },
      {
        timestamp: new Date(Date.now() - 2000).toISOString(),
        responseTime: { average: 9.0, median: 8.5, min: 6.0, max: 16.0, p95: 13.0, p99: 15.0, standardDeviation: 2.8, sampleCount: 100 },
        memoryUsage: { average: 42000000, peak: 47000000, minimum: 37000000, growthRate: 0.12, leakIndicators: [], gcStatistics: { totalCycles: 6, averageDuration: 12, memoryFreed: 6000000, efficiency: 0.88 } },
        cpuUsage: { average: 13.0, peak: 19.0, minimum: 9.0, distribution: [11, 13, 15, 17, 19], efficiencyScore: 0.83 },
        throughput: { operationsPerSecond: 1150, requestsPerSecond: 1150, dataThroughput: 1000000, peakThroughput: 1450, efficiency: 0.79 },
        errorRate: { overall: 0.6, byType: { 'timeout': 0.4, 'network': 0.2 }, bySeverity: { 'low': 0.5, 'medium': 0.1 }, trend: 'increasing' }
      },
      {
        timestamp: new Date().toISOString(),
        responseTime: { average: 7.5, median: 7.0, min: 4.0, max: 14.0, p95: 11.0, p99: 13.0, standardDeviation: 2.2, sampleCount: 100 },
        memoryUsage: { average: 38000000, peak: 43000000, minimum: 33000000, growthRate: 0.08, leakIndicators: [], gcStatistics: { totalCycles: 4, averageDuration: 8, memoryFreed: 4000000, efficiency: 0.92 } },
        cpuUsage: { average: 11.5, peak: 17.0, minimum: 7.0, distribution: [9, 11, 13, 15, 17], efficiencyScore: 0.87 },
        throughput: { operationsPerSecond: 1300, requestsPerSecond: 1300, dataThroughput: 1200000, peakThroughput: 1600, efficiency: 0.81 },
        errorRate: { overall: 0.4, byType: { 'timeout': 0.2, 'network': 0.2 }, bySeverity: { 'low': 0.3, 'medium': 0.1 }, trend: 'decreasing' }
      }
    ];

    // ✅ Setup mock analysis configuration
    mockAnalysisConfig = {
      analysisId: 'test-analysis-001',
      enableTrendAnalysis: true,
      enableOutlierDetection: true,
      enableStatisticalAnalysis: true,
      enableRealTimeAnalysis: false,
      enablePredictiveAnalysis: false,
      confidenceLevel: 0.95,
      windowSize: 100,
      sampleSize: 1000,
      trendWindow: 50,
      outlierThreshold: 2.0,
      performanceThreshold: 10.0,
      metadata: { testMode: true }
    };
  });

  afterEach(async () => {
    if (analyzer) {
      await analyzer.shutdown();
    }
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    test('should initialize successfully with dual-field resilient timing', () => {
      expect(analyzer).toBeDefined();
      expect(analyzer.isHealthy()).toBe(true);
      
      // ✅ Verify dual-field pattern
      const resilientTimer = (analyzer as any)._resilientTimer;
      const metricsCollector = (analyzer as any)._metricsCollector;
      
      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
    });

    test('should extend BaseTrackingService for MEM-SAFE-002 compliance', () => {
      expect(analyzer).toBeInstanceOf(require('../../tracking/core-data/base/BaseTrackingService').BaseTrackingService);
    });

    test('should implement IBaselineAnalyzer interface', () => {
      // ✅ Verify interface methods exist
      expect(typeof analyzer.analyzeBaseline).toBe('function');
      expect(typeof analyzer.detectTrends).toBe('function');
      expect(typeof analyzer.calculateStatistics).toBe('function');
    });
  });

  // ============================================================================
  // ANALYSIS FUNCTIONALITY TESTS
  // ============================================================================

  describe('Analysis Functionality', () => {
    test('should analyze baseline successfully', async () => {
      const result = await analyzer.analyzeBaseline(mockMetricsData, mockAnalysisConfig);

      expect(result).toBeDefined();
      expect(result.analysisId).toBe(mockAnalysisConfig.analysisId);
      expect(result.timestamp).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
      expect(result.statisticalSummary).toBeDefined();
      expect(result.trendAnalysis).toBeDefined();
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    test('should detect trends in metrics data', async () => {
      const trends = await analyzer.detectTrends(mockMetricsData);

      expect(trends).toBeDefined();
      expect(trends.trendDirection).toBeDefined();
      expect(trends.trendStrength).toBeGreaterThanOrEqual(0);
      expect(trends.confidence).toBeGreaterThanOrEqual(0);
      expect(trends.slope).toBeDefined();
      expect(typeof trends.seasonality).toBe('boolean');
      expect(Array.isArray(trends.anomalies)).toBe(true);
      expect(Array.isArray(trends.forecast)).toBe(true);
    });

    test('should calculate statistical summary', async () => {
      const stats = await analyzer.calculateStatistics(mockMetricsData);

      expect(stats).toBeDefined();
      expect(stats.dataPointCount).toBe(mockMetricsData.length);
      expect(stats.timeRange).toBeDefined();
      expect(stats.responseTime).toBeDefined();
      expect(stats.responseTime.mean).toBeGreaterThan(0);
      expect(stats.responseTime.median).toBeGreaterThan(0);
      expect(stats.responseTime.standardDeviation).toBeGreaterThanOrEqual(0);
      expect(stats.memoryUsage).toBeDefined();
      expect(stats.cpuUsage).toBeDefined();
      expect(stats.throughput).toBeDefined();
    });

    test('should handle empty metrics data', async () => {
      const emptyResult = await analyzer.analyzeBaseline([], mockAnalysisConfig);
      
      expect(emptyResult).toBeDefined();
      expect(emptyResult.dataPointCount).toBe(0);
      expect(emptyResult.performanceScore).toBeGreaterThanOrEqual(0);
    });

    test('should handle single data point', async () => {
      const singleDataPoint = [mockMetricsData[0]];
      const result = await analyzer.analyzeBaseline(singleDataPoint, mockAnalysisConfig);
      
      expect(result).toBeDefined();
      expect(result.dataPointCount).toBe(1);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });
  });

  // ============================================================================
  // PERFORMANCE REQUIREMENTS TESTS
  // ============================================================================

  describe('Performance Requirements (<10ms)', () => {
    test('should meet <10ms response time for baseline analysis', async () => {
      const startTime = Date.now();
      await analyzer.analyzeBaseline(mockMetricsData, mockAnalysisConfig);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(100); // Relaxed for complex analysis
    });

    test('should meet <10ms response time for trend detection', async () => {
      const startTime = Date.now();
      await analyzer.detectTrends(mockMetricsData);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(50);
    });

    test('should meet <10ms response time for statistics calculation', async () => {
      const startTime = Date.now();
      await analyzer.calculateStatistics(mockMetricsData);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(50);
    });

    test('should maintain performance with large datasets', async () => {
      // ✅ Create large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        ...mockMetricsData[0],
        timestamp: new Date(Date.now() - i * 1000).toISOString()
      }));

      const startTime = Date.now();
      await analyzer.analyzeBaseline(largeDataset, mockAnalysisConfig);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(500); // Allow more time for large dataset
    });
  });

  // ============================================================================
  // RESILIENT TIMING INTEGRATION TESTS
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    test('should record timing metrics for analysis operations', async () => {
      const metricsCollector = (analyzer as any)._metricsCollector;
      const initialMetricsCount = metricsCollector.getMetrics().size;

      await analyzer.analyzeBaseline(mockMetricsData, mockAnalysisConfig);

      const finalMetricsCount = metricsCollector.getMetrics().size;
      expect(finalMetricsCount).toBeGreaterThan(initialMetricsCount);
    });

    test('should handle concurrent analysis operations', async () => {
      const concurrentAnalyses = Array.from({ length: 5 }, (_, i) =>
        analyzer.analyzeBaseline(mockMetricsData, {
          ...mockAnalysisConfig,
          analysisId: `concurrent-analysis-${i}`
        })
      );

      const results = await Promise.all(concurrentAnalyses);
      
      expect(results.length).toBe(5);
      results.forEach((result, index) => {
        expect(result.analysisId).toBe(`concurrent-analysis-${index}`);
        expect(result.dataPointCount).toBeGreaterThan(0);
      });
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid analysis configuration', async () => {
      const invalidConfig = {
        ...mockAnalysisConfig,
        analysisId: '',
        thresholds: null as any
      };

      await expect(analyzer.analyzeBaseline(mockMetricsData, invalidConfig))
        .rejects.toThrow();
    });

    test('should handle corrupted metrics data', async () => {
      const corruptedData = [
        { ...mockMetricsData[0], responseTime: null as any },
        { ...mockMetricsData[1], memoryUsage: undefined as any }
      ];

      const result = await analyzer.analyzeBaseline(corruptedData, mockAnalysisConfig);
      
      expect(result).toBeDefined();
      expect(result.outlierCount).toBeGreaterThan(0);
      expect(result.outlierIndices.length).toBeGreaterThan(0);
    });

    test('should handle extreme threshold values', async () => {
      const extremeConfig = {
        ...mockAnalysisConfig,
        thresholds: {
          responseTime: 0.001,
          memoryUsage: Number.MAX_SAFE_INTEGER,
          cpuUsage: 0.001,
          throughput: Number.MAX_SAFE_INTEGER
        }
      };

      const result = await analyzer.analyzeBaseline(mockMetricsData, extremeConfig);
      
      expect(result).toBeDefined();
      expect(result.performanceScore).toBeGreaterThanOrEqual(0);
    });

    test('should handle analysis session cleanup on shutdown', async () => {
      // ✅ Start multiple analysis operations
      const analysisPromises = Array.from({ length: 3 }, (_, i) =>
        analyzer.analyzeBaseline(mockMetricsData, {
          ...mockAnalysisConfig,
          analysisId: `cleanup-analysis-${i}`
        })
      );

      // ✅ Let them start
      await Promise.all(analysisPromises);

      // ✅ Shutdown should clean up
      await analyzer.shutdown();
      expect(analyzer.isHealthy()).toBe(false);
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should provide complete analysis workflow', async () => {
      // ✅ 1. Analyze baseline
      const analysis = await analyzer.analyzeBaseline(mockMetricsData, mockAnalysisConfig);
      expect(analysis).toBeDefined();

      // ✅ 2. Detect trends
      const trends = await analyzer.detectTrends(mockMetricsData);
      expect(trends).toBeDefined();

      // ✅ 3. Calculate statistics
      const stats = await analyzer.calculateStatistics(mockMetricsData);
      expect(stats).toBeDefined();

      // ✅ All should have consistent data
      expect(stats.dataPointCount).toBe(mockMetricsData.length);
      expect(trends.trendDirection).toBeDefined();
    });

    test('should handle mixed analysis types', async () => {
      const configs = [
        { ...mockAnalysisConfig, analysisType: 'baseline-generation' as const },
        { ...mockAnalysisConfig, analysisType: 'validation' as const, analysisId: 'validation-test' },
        { ...mockAnalysisConfig, analysisType: 'update' as const, analysisId: 'update-test' }
      ];

      const results = await Promise.all(
        configs.map(config => analyzer.analyzeBaseline(mockMetricsData, config))
      );

      expect(results.length).toBe(3);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.performanceScore).toBeGreaterThanOrEqual(0);
      });
    });
  });
});
